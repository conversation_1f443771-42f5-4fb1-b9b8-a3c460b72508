{% extends '../../base.html.twig' %}

{% block title %}Register{% endblock %}

{% block body %}
    <section class="bg-gray-50 dark:bg-gray-900">
        <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
            <a href="#" class="flex items-center mb-6 text-2xl font-semibold text-gray-900 dark:text-white">
                FlexOffice
            </a>
            <div class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
                <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
                    <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">
                        Create an account
                    </h1>
                    {{ form_start(registrationForm, {'attr': {'class': 'space-y-4 md:space-y-6'}}) }}
                    <div>
                        {% include 'elements/form/input.html.twig' with { 'element': registrationForm.email, 'label':'Email'} %}
                    </div>
                    <div class="flex space-x-4">
                        <div class="w-1/2">
                            {% include 'elements/form/input.html.twig' with { 'element': registrationForm.firstname, 'label':'Firstname'} %}
                        </div>
                        <div class="w-1/2">
                            {% include 'elements/form/input.html.twig' with { 'element': registrationForm.lastname, 'label':'Lastname'} %}
                        </div>
                    </div>
                    <div>
                        {% include 'elements/form/input.html.twig' with {'element': registrationForm.plainPassword, 'label': 'Password'} %}
                    </div>
                    <div>
                        <label for="confirm-password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Confirm password</label>
                        <input type="password" name="confirm-password" id="confirm-password" placeholder="" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required="">
                    </div>
                    <div class="mt-4 form-group">
                        {% include 'elements/form/radio.html.twig' with {'element': registrationForm.userRole, 'label': 'Role'} %}
                    </div>
                    <div class="flex items-start">
                        {% include 'elements/form/checkbox.html.twig' with { 'element': registrationForm.agreeTerms } %}
                    </div>
                    {% include 'elements/form/submit.html.twig' with {'text': 'Create Account'} %}
                    <p class="text-sm font-light text-gray-500 dark:text-gray-400">
                        Already have an account? <a href="#" class="font-medium text-primary-600 hover:underline dark:text-primary-500">Login here</a>
                    </p>
                    {{ form_end(registrationForm) }}
                </div>
            </div>
        </div>
    </section>
{% endblock %}