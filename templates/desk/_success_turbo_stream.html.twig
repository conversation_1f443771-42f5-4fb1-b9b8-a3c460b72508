<turbo-stream action="replace" target="deskModal">
    <template>
        <div id="deskModal" class="hidden"></div>
    </template>
</turbo-stream>

<turbo-stream action="prepend" target="flash-messages">
    <template>
        {% include 'shared/components/_flash_message.html.twig' with {
            message: 'Bureau créé avec succès !',
            type: 'success'
        } %}
    </template>
</turbo-stream>

<turbo-stream action="replace" target="desks-container">
    <template>
        <div id="desks-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for desk in desks %}
                {% if desk.isAvailable %}
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ desk.name }}</h3>
                            <p class="text-gray-700 dark:text-gray-300 mb-4">{{ desk.description|slice(0, 100) }}{% if desk.description|length > 100 %}...{% endif %}</p>

                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded dark:bg-blue-900 dark:text-blue-300">
                                    Capacity: {{ desk.capacity }}
                                </span>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded dark:bg-green-900 dark:text-green-300">
                                    {{ desk.pricePerDay }}€ / day
                                </span>
                            </div>

                            <div class="flex justify-end">
                                {% if is_guest() %}
                                {% include 'elements/link_button.html.twig' with {
                                    'text': 'Reserve',
                                    'href': path('app_reservation_new', {'id': desk.id}),
                                    'size': 'sm'
                                } %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-700 dark:text-gray-300">No desks available in this space.</p>
                </div>
            {% endfor %}
        </div>
    </template>
</turbo-stream>
