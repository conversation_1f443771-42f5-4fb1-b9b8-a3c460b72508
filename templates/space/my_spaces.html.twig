{% extends 'base.html.twig' %}

{% block title %}{{ 'My Spaces'|trans }}{% endblock %}

{% block content %}
            <div class="container mx-auto">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ 'My Spaces'|trans }}</h1>
                    {% include 'shared/elements/link_button.html.twig' with {
                        'text': 'Ajouter un Nouvel Espace'|trans,
                        'href': path('app_space_new'),
                        'variant': 'success',
                        'icon_before': 'plus'
                    } %}
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for space in spaces %}
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                            <div class="p-5">
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ space.name }}</h2>
                                <p class="text-gray-700 dark:text-gray-300 mb-4">{{ space.description|slice(0, 100) }}{% if space.description|length > 100 %}...{% endif %}</p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded dark:bg-blue-900 dark:text-blue-300">
                                        {{ space.desks|length }} {{ 'Desks'|trans }}
                                    </span>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded dark:bg-gray-700 dark:text-gray-300">
                                        {{ space.address.city }}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <a href="{{ path('app_space_edit', {'id': space.id}) }}" class="p-2 text-green-600 hover:text-green-700 dark:text-green-500 dark:hover:text-green-400 transition-colors" title="Edit Space">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
{#                                        <button data-modal-target="deleteSpaceModal-{{ space.id }}" data-modal-toggle="deleteSpaceModal-{{ space.id }}" class="p-2 text-red-600 hover:text-red-700 dark:text-red-500 dark:hover:text-red-400 transition-colors" title="Delete Space">#}
{#                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">#}
{#                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>#}
{#                                            </svg>#}
{#                                        </button>#}
                                    </div>
                                    <a href="{{ path('app_space_show', {'id': space.id}) }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors shadow-sm">
                                        Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="col-span-full text-center py-8">
                            <p class="text-gray-700 dark:text-gray-300">You haven't created any spaces yet.</p>
                            <div class="mt-4">
                                {% include 'shared/elements/link_button.html.twig' with {
                                    'text': 'Create Your First Space',
                                    'href': path('app_space_new'),
                                    'variant': 'success'
                                } %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

    <!-- Delete Confirmation Modals (Flowbite) -->
    {% for space in spaces %}
        <!-- Modal for space {{ space.id }} -->
        <div id="deleteSpaceModal-{{ space.id }}" tabindex="-1" aria-hidden="true" class="hidden modal-container">
            <div class="modal-content modal-lg">
                <!-- Modal content -->
                <!-- Modal header -->
                <div class="modal-header">
                    <h3 class="modal-title">Delete Space</h3>
                    <button type="button" class="modal-close" data-modal-hide="deleteSpaceModal-{{ space.id }}">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="modal-body modal-body-center">
                    <svg class="modal-icon modal-icon-danger" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                    </svg>
                    <h3 class="modal-text modal-text-title">
                        Are you sure you want to delete the space "<span class="modal-text-highlight">{{ space.name }}</span>"?
                    </h3>
                    <p class="modal-text modal-text-description">
                        This action cannot be undone and will also delete all associated desks and reservations.
                    </p>
                </div>
                <!-- Modal footer -->
                <div class="modal-footer">
                    <button data-modal-hide="deleteSpaceModal-{{ space.id }}" type="button" class="modal-btn modal-btn-secondary">
                        No, cancel
                    </button>
                    <form method="POST" action="{{ path('app_space_delete', {'id': space.id}) }}" class="inline">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete_space') }}">
                        <input type="hidden" name="_method" value="DELETE">
                        <button type="submit" class="modal-btn modal-btn-danger">
                            Yes, I'm sure
                        </button>
                    </form>
                </div>
            </div>
        </div>
    {% endfor %}
{% endblock %}
