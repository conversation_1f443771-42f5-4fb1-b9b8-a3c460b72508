{% extends 'base.html.twig' %}

{% block title %}My Reservations{% endblock %}

{% block content %}
            <div class="container mx-auto">
                <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">My Reservations</h1>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Espace</th>
                                    <th scope="col" class="px-6 py-3">Bureau</th>
                                    <th scope="col" class="px-6 py-3">Date</th>
                                    <th scope="col" class="px-6 py-3">Statut</th>
                                    <th scope="col" class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reservation in reservations %}
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                                            {{ reservation.desk.space.name }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ reservation.desk.name }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ reservation.reservationDate ? reservation.reservationDate|date('Y-m-d') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {% if reservation.status == 0 %}
                                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">Pending</span>
                                            {% elseif reservation.status == 1 %}
                                                <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Confirmed</span>
                                            {% elseif reservation.status == 2 %}
                                                <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Cancelled</span>
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ path('app_reservation_show', {'id': reservation.id}) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline" title="View Reservation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                </a>
                                                {% if reservation.status != 2 %}
                                                    <a href="{{ path('app_reservation_cancel', {'id': reservation.id}) }}" class="font-medium text-red-600 dark:text-red-500 hover:underline">Cancel</a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td colspan="5" class="px-6 py-4 text-center">
                                            No reservations found. <a href="{{ path('app_space_index') }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Browse spaces</a> to make a reservation.
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

    <!-- Delete Confirmation Modals (Flowbite) -->
    {% for reservation in reservations %}
        {% if reservation.status != 2 %}
            <!-- Modal for reservation {{ reservation.id }} -->
            <div id="deleteReservationModal-{{ reservation.id }}" tabindex="-1" aria-hidden="true" class="hidden modal-container">
                <div class="modal-content modal-lg">
                    <!-- Modal content -->
                    <!-- Modal header -->
                    <div class="modal-header">
                        <h3 class="modal-title">Delete Reservation</h3>
                        <button type="button" class="modal-close" data-modal-hide="deleteReservationModal-{{ reservation.id }}">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <!-- Modal body -->
                    <div class="modal-body modal-body-center">
                        <svg class="modal-icon modal-icon-danger" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                        </svg>
                        <h3 class="modal-text modal-text-title">
                            Are you sure you want to delete this reservation?
                        </h3>
                        <div class="modal-text modal-text-description">
                            <p class="mb-3">
                                <strong>Bureau:</strong> <span class="modal-text-highlight">{{ reservation.desk.name }}</span><br>
                                <strong>Date:</strong> <span class="modal-text-highlight">{{ reservation.reservationDate|date('Y-m-d') }}</span><br>
                                <strong>Espace:</strong> <span class="modal-text-highlight">{{ reservation.desk.space.name }}</span>
                            </p>
                            <p>This action cannot be undone.</p>
                        </div>
                    </div>
                    <!-- Modal footer -->
                    <div class="modal-footer">
                        <button data-modal-hide="deleteReservationModal-{{ reservation.id }}" type="button" class="modal-btn modal-btn-secondary">
                            No, cancel
                        </button>
                        <form method="POST" action="{{ path('app_reservation_delete', {'id': reservation.id}) }}" class="inline">
                            <input type="hidden" name="_token" value="{{ csrf_token('delete_reservation') }}">
                            <input type="hidden" name="_method" value="DELETE">
                            <button type="submit" class="modal-btn modal-btn-danger">
                                Yes, I'm sure
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
{% endblock %}
