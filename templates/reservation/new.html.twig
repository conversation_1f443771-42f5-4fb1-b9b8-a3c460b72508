{% extends 'base.html.twig' %}

{% block title %}Nouvelle Réservation{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block content %}
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Réserver {{ desk.name }}</h1>
                <p class="text-gray-600 dark:text-gray-400">Nouvelle réservation</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ path('app_space_show', {'id': desk.space.id}) }}"
                   class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors flex items-center font-medium shadow-md">
                    <svg class="w-5 h-5 mr-2" stroke="currentColor" viewBox="0 0 24 24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Retour au bureau
                </a>
            </div>
        </div>
        <div class="bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800 p-6">
            {{ form_start(reservation_form, {'method': 'POST'}) }}
            <div class="flex justify-center">
                {{ form_row(reservation_form.reservationDate) }}
            </div>
            <div class="flex justify-end space-x-3">
                <a href="{{ path('app_space_show', {'id': desk.space.id}) }}" class="form-btn-cancel">Annuler</a>
                {{ form_widget(reservation_form.save, {'attr': {'class': 'px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 transition-colors font-medium shadow-md'}, 'label': 'Confirmer la réservation'}) }}
            </div>
            {{ form_end(reservation_form, {'render_rest': false}) }}
        </div>
    </div>
{% endblock %}


{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const bookedDates = {{ booked_dates|raw }};
            document.querySelectorAll('.datepicker').forEach((el) => {
                flatpickr(el, {
                    locale: "fr",
                    inline: true,
                    dateFormat: "Y-m-d",
                    disable: bookedDates,
                    onChange: function (selectedDates, dateStr, instance) {
                        // Met à jour la valeur du champ input
                        el.value = dateStr;
                        // Déclenche un événement change pour Symfony
                        el.dispatchEvent(new Event('change', {bubbles: true}));
                    }
                });
            });
        });
    </script>
{% endblock %}

