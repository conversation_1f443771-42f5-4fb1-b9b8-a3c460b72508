{% if label is defined %}
    {{ form_label(element, label, {'label_attr': {'class': 'form-label'}}) }}
{% else %}
    {{ form_label(element, null, {'label_attr': {'class': 'form-label'}}) }}
{% endif %}
<div class="form-radio-container">
    {% for child in element %}
        <div class="form-radio-item">
            <div class="flex items-center h-5">
                {{ form_widget(child, {'attr': {
                    'class': 'form-radio-input'
                }}) }}
            </div>
            <div class="form-radio-label">
                {{ form_label(child, null, {'label_attr': {'class': 'font-medium'}}) }}
            </div>
        </div>
    {% endfor %}
</div>
{{ form_errors(element, {'attr': {'class': 'form-error'}}) }}
