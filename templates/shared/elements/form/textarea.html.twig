{% if label is defined %}
    {{ form_label(element, label, {'label_attr': {'class': 'block mb-2 text-sm font-medium text-gray-900 dark:text-white'}}) }}
{% else %}
    {{ form_label(element, null, {'label_attr': {'class': 'block mb-2 text-sm font-medium text-gray-900 dark:text-white'}}) }}
{% endif %}
{{ form_widget(element, {'attr': {
    'class': 'block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500',
    'placeholder': placeholder|default(''),
    'rows': rows|default(4)
}}) }}
{{ form_errors(element, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}
