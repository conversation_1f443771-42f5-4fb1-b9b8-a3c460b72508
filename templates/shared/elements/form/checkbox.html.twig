<div class="flex items-center">
    <div class="flex items-center h-5">
        {{ form_widget(element, {'attr':
            {'class': 'w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800'}}) }}
    </div>
    <div class="ml-3 text-sm">
        {% if label is defined %}
            {{ form_label(element, label, {'label_attr': {'class': 'font-light text-gray-500 dark:text-gray-300'}}) }}
        {% else %}
            {{ form_label(element, null, {'label_attr': {'class': 'font-light text-gray-500 dark:text-gray-300'}}) }}
        {% endif %}
    </div>
</div>
{{ form_errors(element, {'attr': {'class': 'text-red-500 text-sm mt-1'}}) }}