<a
    href="{{ href }}"
    class="
        {% if variant is defined %}
            {% if variant == 'primary' %}
                text-white bg-primary hover:bg-primary-600 focus:ring-4 focus:outline-none focus:ring-primary/30 dark:bg-primary-600 dark:hover:bg-primary dark:focus:ring-primary/80
            {% elseif variant == 'secondary' %}
                text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700
            {% elseif variant == 'success' %}
                text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800
            {% elseif variant == 'danger' %}
                text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800
            {% elseif variant == 'warning' %}
                text-white bg-yellow-400 hover:bg-yellow-500 focus:ring-4 focus:outline-none focus:ring-yellow-300 dark:bg-yellow-600 dark:hover:bg-yellow-700 dark:focus:ring-yellow-800
            {% elseif variant == 'info' %}
                text-white bg-blue-500 hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800
            {% elseif variant == 'outline-primary' %}
                text-primary hover:text-white border border-primary hover:bg-primary focus:ring-4 focus:outline-none focus:ring-primary/30 dark:border-primary-600 dark:text-primary-600 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary/80
            {% else %}
                text-white bg-primary hover:bg-primary-600 focus:ring-4 focus:outline-none focus:ring-primary/30 dark:bg-primary-600 dark:hover:bg-primary dark:focus:ring-primary/80
            {% endif %}
        {% else %}
            text-white bg-primary hover:bg-primary-600 focus:ring-4 focus:outline-none focus:ring-primary/30 dark:bg-primary-600 dark:hover:bg-primary dark:focus:ring-primary/80
        {% endif %}

        {% if size is defined %}
            {% if size == 'xs' %}
                text-xs px-2 py-1
            {% elseif size == 'sm' %}
                text-sm px-3 py-1.5
            {% elseif size == 'lg' %}
                text-lg px-5 py-3
            {% elseif size == 'xl' %}
                text-xl px-6 py-3.5
            {% else %}
                text-sm px-5 py-2.5
            {% endif %}
        {% else %}
            text-sm px-5 py-2.5
        {% endif %}

        font-medium rounded-lg text-center inline-flex items-center
        {{ classes|default('') }}
    "
    {% if attributes is defined %}{{ attributes|raw }}{% endif %}
>
    {% if icon_before is defined and icon_before %}
        {% if icon_before == 'plus' %}
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
        {% elseif icon_before == 'arrow-left' %}
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        {% elseif icon_before == 'calendar' %}
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
        {% endif %}
    {% endif %}

    {{ text }}

    {% if icon_after is defined and icon_after %}
        {% if icon_after == 'arrow-right' %}
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        {% endif %}
    {% endif %}
</a>
