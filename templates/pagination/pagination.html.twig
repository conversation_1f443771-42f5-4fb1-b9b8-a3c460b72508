{# Pagination template personnalisé avec Tailwind CSS #}
{% if pageCount > 1 %}
    <nav class="flex items-center justify-between pt-4" aria-label="Table navigation">
        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
            Affichage de 
            <span class="font-semibold text-gray-900 dark:text-white">{{ startPage }}</span>
            à 
            <span class="font-semibold text-gray-900 dark:text-white">{{ endPage }}</span>
            sur 
            <span class="font-semibold text-gray-900 dark:text-white">{{ totalCount }}</span>
            résultats
        </span>
        
        <ul class="inline-flex -space-x-px text-sm h-8">
            {# Bouton Précédent #}
            {% if previous is defined %}
                <li>
                    <a href="{{ path(route, query|merge({(pageParameterName): previous})) }}" 
                       class="flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Précédent
                    </a>
                </li>
            {% else %}
                <li>
                    <span class="flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-300 bg-white border border-gray-300 rounded-l-lg cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-600">
                        Précédent
                    </span>
                </li>
            {% endif %}

            {# Pages #}
            {% for page in pagesInRange %}
                {% if page != current %}
                    <li>
                        <a href="{{ path(route, query|merge({(pageParameterName): page})) }}" 
                           class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            {{ page }}
                        </a>
                    </li>
                {% else %}
                    <li>
                        <span class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">
                            {{ page }}
                        </span>
                    </li>
                {% endif %}
            {% endfor %}

            {# Bouton Suivant #}
            {% if next is defined %}
                <li>
                    <a href="{{ path(route, query|merge({(pageParameterName): next})) }}" 
                       class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                        Suivant
                    </a>
                </li>
            {% else %}
                <li>
                    <span class="flex items-center justify-center px-3 h-8 leading-tight text-gray-300 bg-white border border-gray-300 rounded-r-lg cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-600">
                        Suivant
                    </span>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
