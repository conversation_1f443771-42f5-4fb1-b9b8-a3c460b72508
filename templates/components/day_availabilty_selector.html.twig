{% macro render_day_availabilty_selector(form, title) %}
    <div class="availability-widget">
        <h4 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">{{ title|default('Disponibilité') }}</h4>

        {# Jo<PERSON> de la semaine #}
        <div class="grid grid-cols-7 gap-3">
            {% set days = {
                'monday': 'Lun',
                'tuesday': 'Mar',
                'wednesday': 'Mer',
                'thursday': 'Jeu',
                'friday': 'Ven',
                'saturday': 'Sam',
                'sunday': 'Dim'
            } %}

            {% for day_key, day_label in days %}
                {% if attribute(form, day_key) is defined %}
                    {% set field = attribute(form, day_key) %}
                    <div class="day-selector-item">
                        {{ form_widget(field, {'attr': {'class': 'day-selector-checkbox'}}) }}
                        <label for="{{ field.vars.id }}" class="day-selector-label">
                            <div class="day-selector-circle">{{ day_label|slice(0, 1) }}
                            </div>
                            <span class="day-selector-text">{{ day_label }}</span>
                        </label>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
{% endmacro %}