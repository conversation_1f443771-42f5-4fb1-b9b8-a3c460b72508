{% extends 'base.html.twig' %}

{% block title %}Admin - {{ 'Reservations'|trans }}{% endblock %}

{% block content %}
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gestion des Réservations</h1>
            <a href="{{ path('app_admin_dashboard') }}"
               class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors flex items-center font-medium shadow-md">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                     xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Retourner au Dashboard
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">{{ 'ID'|trans }}</th>
                        <th scope="col" class="px-6 py-3">{{ 'Guest'|trans }}</th>
                        <th scope="col" class="px-6 py-3">{{ 'Desk'|trans }}</th>
                        <th scope="col" class="px-6 py-3">{{ 'Space'|trans }}</th>
                        <th scope="col" class="px-6 py-3">{{ 'Date'|trans }}</th>
                        <th scope="col" class="px-6 py-3">{{ 'Status'|trans }}</th>
                        <th scope="col" class="px-6 py-3">{{ 'Actions'|trans }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for reservation in reservations %}
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4">{{ reservation.id }}</td>
                            <td class="px-6 py-4">{{ reservation.guest.firstname }} {{ reservation.guest.lastname }}</td>
                            <td class="px-6 py-4">{{ reservation.desk.name }}</td>
                            <td class="px-6 py-4">{{ reservation.desk.space.name }}</td>
                            <td class="px-6 py-4">{{ reservation.reservationDate|date('Y-m-d H:i') }}</td>
                            <td class="px-6 py-4">
                                {% if reservation.status == 0 %}
                                    <span class="bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">Pending</span>
                                {% elseif reservation.status == 1 %}
                                    <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Confirmed</span>
                                {% elseif reservation.status == 2 %}
                                    <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Cancelled</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% if reservation.status != 2 %}
                                    <div class="flex items-center space-x-2">
                                        <button data-modal-target="deleteReservationModal-{{ reservation.id }}"
                                                data-modal-toggle="deleteReservationModal-{{ reservation.id }}"
                                                class="font-medium text-red-600 dark:text-red-500 hover:underline"
                                                title="Annuler Reservation">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ knp_pagination_render(reservations) }}
        </div>
    </div>

    {% for reservation in reservations %}
        <div id="deleteReservationModal-{{ reservation.id }}" tabindex="-1" aria-hidden="true"
             class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-2xl max-h-full">
                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                    <button type="button"
                            class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                            data-modal-hide="deleteModal-{{ reservation.id }}">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                             viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                    <div class="p-4 md:p-5 text-center mb-3">
                        <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                        </svg>
                        <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                            {{ 'Are you sure you want to delete this reservation?'|trans }}
                        </h3>
                        <div class="items-center p-4 md:p-5 ">
                            <button data-modal-hide="deleteReservationModal-{{ reservation.id }}" type="button"
                                    class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                                {{ 'No, cancel'|trans }}
                            </button>
                            <form method="POST" action="{{ path('app_reservation_cancel', {'id': reservation.id}) }}"
                                  class="inline">
                                <input type="hidden" name="_token" value="{{ csrf_token('delete_space') }}">
                                <input type="hidden" name="_method" value="DELETE">
                                <button type="submit"
                                        class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                                    {{ "Yes, I'm sure"|trans }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endfor %}
{% endblock %}
{% block javascripts %}
    {{ parent() }}
{% endblock %}
