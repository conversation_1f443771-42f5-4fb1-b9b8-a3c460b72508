import './bootstrap.js';
import './styles/app.css';
import 'flowbite';

import flatpickr from 'flatpickr';
import 'flatpickr/dist/flatpickr.min.css'; // Importe le CSS

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, searching for datepicker elements...');
    const datepickers = document.querySelectorAll('.datepicker');
    console.log('Found datepicker elements:', datepickers.length);

    datepickers.forEach((el) => {
        console.log('Initializing flatpickr on element:', el);
        flatpickr(el, {
            dateFormat: "Y-m-d"
        });
    });
});
