<?php

namespace App\Form;

use App\Entity\Desk;
use App\Entity\Reservation;
use App\Service\AvailabilityChecker;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class ReservationFormType extends AbstractType
{
    private AvailabilityChecker $availabilityChecker;

    public function __construct(AvailabilityChecker $availabilityChecker)
    {
        $this->availabilityChecker = $availabilityChecker;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Desk|null $desk */
        $desk = $options['desk'] ?? null;

        // Get availability data for the desk's space
        $availabilityAttrs = [];
        if ($desk) {
            $availability = $desk->getSpace()->getAvailability();
            if ($availability) {
                $availabilityAttrs = [
                    'data-monday' => $availability->isMonday() ? '1' : '0',
                    'data-tuesday' => $availability->isTuesday() ? '1' : '0',
                    'data-wednesday' => $availability->isWednesday() ? '1' : '0',
                    'data-thursday' => $availability->isThursday() ? '1' : '0',
                    'data-friday' => $availability->isFriday() ? '1' : '0',
                    'data-saturday' => $availability->isSaturday() ? '1' : '0',
                    'data-sunday' => $availability->isSunday() ? '1' : '0',
                    'data-desk-id' => $desk->getId(),
                    'id' => 'reservation_form_reservationDate',
                ];
            }
        }

        $builder
            ->add('reservationDate', DateType::class, [
                'label' => 'Reservation Date',
                'widget' => 'single_text',        // Important : un seul input <input type="text">
                'html5' => false,                 // Important : empêche le navigateur de mettre son propre datepicker
                'format' => 'yyyy-MM-dd',         // Doit correspondre au format Flatpickr (Y-m-d)
                'input' => 'datetime',            // Retourne un objet \DateTime en PHP
                'required' => true,
                'attr' => [
                    'class' => 'datepicker',      // Permet de cibler le champ avec Flatpickr JS
                    'autocomplete' => 'off',      // (Optionnel) désactive l’autocomplétion navigateur
                ],
            ])
            ->add('save', SubmitType::class, [
                'label' => 'Reserve',
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Reservation::class,
            'desk' => null,
        ]);

        $resolver->setAllowedTypes('desk', ['null', Desk::class]);
    }
}
