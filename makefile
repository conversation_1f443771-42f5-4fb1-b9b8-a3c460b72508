# Makefile

# Variables
DOCKER_COMPOSE = docker-compose
PHP_CONTAINER = php

# Symfony commands
sf:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) php bin/console $(cmd)

# Database commands
db-make-migration:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) php bin/console make:migration

db-migrate:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) php bin/console doctrine:migrations:migrate

db-diff:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) php bin/console doctrine:migrations:diff

db-load-fixtures:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) php bin/console doctrine:fixtures:load

db-check-fixtures:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) ./scripts/check-fixtures.sh

test-admin:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) ./scripts/test-admin-access.sh

test-admin-nav:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) ./scripts/test-admin-navigation.sh

test-flowbite-modals:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) ./scripts/test-flowbite-modals.sh

test-admin-buttons:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) ./scripts/test-admin-buttons.sh

test-routes-fix:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) ./scripts/test-routes-fix.sh

# Cache commands
cache-clear:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) php bin/console cache:clear

# Composer commands
composer-install:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) composer install

composer-update:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) composer update

# NPM commands
npm-install:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) npm install

npm-build:
	$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) npm run build

# Usage:
# make sf cmd="command" - Run Symfony command
# make db-migrate - Run database migrations
# make db-diff - Generate migration diff
# make db-load-fixtures - Load database fixtures
# make cache-clear - Clear Symfony cache
# make composer-install - Install Composer dependencies
# make composer-update - Update Composer dependencies
# make npm-install - Install NPM dependencies
# make npm-build - Build assets with NPM